import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Stakeholder } from 'src/entities/stakeholders.entity';
import { CreateStakeholderDto } from 'src/dto/stakeholder/create-stakeholder.dto';
import { UpdateStakeholderDto } from 'src/dto/stakeholder/update-stakeholder.dto';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class StakeholdersService {
  constructor(
    @InjectRepository(Stakeholder)
    private readonly stakeholderRepository: Repository<Stakeholder>,
  ) {}

  async create(dto: CreateStakeholderDto, createdBy: string): Promise<Stakeholder> {
    const stakeholder = this.stakeholderRepository.create({
      ...dto,
      stakeholder_id: uuidv4(),
      created_by: createdBy,
    });
    return await this.stakeholderRepository.save(stakeholder);
  }

  async findAll(): Promise<Stakeholder[]> {
    return await this.stakeholderRepository.find({
      where: { deleted_at: undefined },
    });
  }

  async findOne(id: string): Promise<Stakeholder> {
    const stakeholder = await this.stakeholderRepository.findOne({
      where: { stakeholder_id: id, deleted_at: undefined },
    });

    if (!stakeholder) {
      throw new NotFoundException(`Stakeholder with ID ${id} not found`);
    }

    return stakeholder;
  }

  async update(id: string, dto: UpdateStakeholderDto, updatedBy: string): Promise<Stakeholder> {
    const stakeholder = await this.findOne(id);

    Object.assign(stakeholder, dto, { updated_by: updatedBy });

    return await this.stakeholderRepository.save(stakeholder);
  }

  async softDelete(id: string): Promise<void> {
    const stakeholder = await this.findOne(id);
    await this.stakeholderRepository.softRemove(stakeholder);
  }
}
