import {
  Injectable,
  NotFoundException,
  ConflictException,
  BadRequestException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ClientSystems, ClientSystemStatus } from '../entities/client-systems.entity';
import { CreateClientSystemDto } from '../dto/client-system/create-client-system.dto';
import { UpdateClientSystemDto } from '../dto/client-system/update-client-system.dto';
import { PaginateQuery, paginate, Paginated } from 'nestjs-paginate';

@Injectable()
export class ClientSystemsService {
  constructor(
    @InjectRepository(ClientSystems)
    private readonly clientSystemsRepository: Repository<ClientSystems>,
  ) {}

  async create(
    createClientSystemDto: CreateClientSystemDto,
    userId: string,
  ): Promise<ClientSystems> {
    // Check if system code already exists
    const existingSystem = await this.clientSystemsRepository.findOne({
      where: { system_code: createClientSystemDto.system_code },
    });

    if (existingSystem) {
      throw new ConflictException(
        `Client system with code '${createClientSystemDto.system_code}' already exists`,
      );
    }

    const clientSystem = this.clientSystemsRepository.create({
      ...createClientSystemDto,
      created_by: userId,
    });

    return await this.clientSystemsRepository.save(clientSystem);
  }

  async findAll(query: PaginateQuery): Promise<Paginated<ClientSystems>> {
    const queryBuilder = this.clientSystemsRepository
      .createQueryBuilder('client_systems')
      .leftJoinAndSelect('client_systems.creator', 'creator')
      .leftJoinAndSelect('client_systems.updater', 'updater')
      .orderBy('client_systems.created_at', 'DESC');

    return paginate(query, queryBuilder, {
      sortableColumns: [
        'name',
        'system_code',
        'system_type',
        'status',
        'created_at',
        'updated_at',
      ],
      searchableColumns: [
        'name',
        'system_code',
        'description',
        'organization',
        'contact_email',
      ],
      defaultSortBy: [['created_at', 'DESC']],
      filterableColumns: {
        system_type: true,
        status: true,
        organization: true,
      },
    });
  }

  async findOne(id: string): Promise<ClientSystems> {
    const clientSystem = await this.clientSystemsRepository.findOne({
      where: { client_system_id: id },
      relations: ['creator', 'updater'],
    });

    if (!clientSystem) {
      throw new NotFoundException(`Client system with ID '${id}' not found`);
    }

    return clientSystem;
  }

  async findBySystemCode(systemCode: string): Promise<ClientSystems> {
    const clientSystem = await this.clientSystemsRepository.findOne({
      where: { system_code: systemCode },
      relations: ['creator', 'updater'],
    });

    if (!clientSystem) {
      throw new NotFoundException(
        `Client system with code '${systemCode}' not found`,
      );
    }

    return clientSystem;
  }

  async update(
    id: string,
    updateClientSystemDto: UpdateClientSystemDto,
    userId: string,
  ): Promise<ClientSystems> {
    const clientSystem = await this.findOne(id);

    // Check if system code is being updated and if it conflicts
    if (
      updateClientSystemDto.system_code &&
      updateClientSystemDto.system_code !== clientSystem.system_code
    ) {
      const existingSystem = await this.clientSystemsRepository.findOne({
        where: { system_code: updateClientSystemDto.system_code },
      });

      if (existingSystem) {
        throw new ConflictException(
          `Client system with code '${updateClientSystemDto.system_code}' already exists`,
        );
      }
    }

    // Update the client system
    Object.assign(clientSystem, {
      ...updateClientSystemDto,
      updated_by: userId,
    });

    return await this.clientSystemsRepository.save(clientSystem);
  }

  async remove(id: string): Promise<void> {
    const clientSystem = await this.findOne(id);
    await this.clientSystemsRepository.softDelete(id);
  }

  async updateLastAccessed(id: string): Promise<void> {
    await this.clientSystemsRepository.update(id, {
      last_accessed_at: new Date(),
    });
  }

  async getSystemStats(): Promise<{
    total: number;
    active: number;
    inactive: number;
    maintenance: number;
    deprecated: number;
    byType: Record<string, number>;
  }> {
    const [total, active, inactive, maintenance, deprecated] = await Promise.all([
      this.clientSystemsRepository.count(),
      this.clientSystemsRepository.count({ where: { status: ClientSystemStatus.ACTIVE } }),
      this.clientSystemsRepository.count({ where: { status: ClientSystemStatus.INACTIVE } }),
      this.clientSystemsRepository.count({ where: { status: ClientSystemStatus.MAINTENANCE } }),
      this.clientSystemsRepository.count({ where: { status: ClientSystemStatus.DEPRECATED } }),
    ]);

    // Get counts by type
    const typeStats = await this.clientSystemsRepository
      .createQueryBuilder('client_systems')
      .select('client_systems.system_type', 'type')
      .addSelect('COUNT(*)', 'count')
      .groupBy('client_systems.system_type')
      .getRawMany();

    const byType = typeStats.reduce((acc, stat) => {
      acc[stat.type] = parseInt(stat.count);
      return acc;
    }, {});

    return {
      total,
      active,
      inactive,
      maintenance,
      deprecated,
      byType,
    };
  }
}
