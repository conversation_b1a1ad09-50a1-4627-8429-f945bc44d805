import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
  ParseU<PERSON><PERSON>ipe,
  Patch,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { ClientSystemsService } from './client-systems.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { CreateClientSystemDto } from '../dto/client-system/create-client-system.dto';
import { UpdateClientSystemDto } from '../dto/client-system/update-client-system.dto';
import { ClientSystems } from '../entities/client-systems.entity';
import { Paginate, PaginateQuery } from 'nestjs-paginate';
import { PaginatedResult, PaginationTransformer } from '../common/interfaces/pagination.interface';
import { Audit } from '../common/interceptors/audit.interceptor';
import { AuditAction, AuditModule } from '../entities/audit-trail.entity';

@ApiTags('client-systems')
@Controller('client-systems')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
export class ClientSystemsController {
  constructor(private readonly clientSystemsService: ClientSystemsService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new client system' })
  @ApiResponse({
    status: 201,
    description: 'Client system created successfully',
    type: ClientSystems,
  })
  @Audit({
    action: AuditAction.CREATE,
    module: AuditModule.SYSTEM_MANAGEMENT,
    resourceType: 'ClientSystem',
    description: 'Created new client system',
  })
  async create(
    @Body() createClientSystemDto: CreateClientSystemDto,
    @Request() req: any,
  ): Promise<ClientSystems> {
    return this.clientSystemsService.create(createClientSystemDto, req.user.userId);
  }

  @Get()
  @ApiOperation({ summary: 'Get all client systems with pagination' })
  @ApiResponse({
    status: 200,
    description: 'Client systems retrieved successfully',
  })
  async findAll(@Paginate() query: PaginateQuery): Promise<PaginatedResult<ClientSystems>> {
    const result = await this.clientSystemsService.findAll(query);
    return PaginationTransformer.transform(result);
  }

  @Get('stats')
  @ApiOperation({ summary: 'Get client systems statistics' })
  @ApiResponse({
    status: 200,
    description: 'Client systems statistics retrieved successfully',
  })
  async getStats() {
    return this.clientSystemsService.getSystemStats();
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a client system by ID' })
  @ApiParam({ name: 'id', description: 'Client system UUID' })
  @ApiResponse({
    status: 200,
    description: 'Client system retrieved successfully',
    type: ClientSystems,
  })
  async findOne(@Param('id', ParseUUIDPipe) id: string): Promise<ClientSystems> {
    return this.clientSystemsService.findOne(id);
  }

  @Get('code/:systemCode')
  @ApiOperation({ summary: 'Get a client system by system code' })
  @ApiParam({ name: 'systemCode', description: 'Client system code' })
  @ApiResponse({
    status: 200,
    description: 'Client system retrieved successfully',
    type: ClientSystems,
  })
  async findBySystemCode(@Param('systemCode') systemCode: string): Promise<ClientSystems> {
    return this.clientSystemsService.findBySystemCode(systemCode);
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update a client system' })
  @ApiParam({ name: 'id', description: 'Client system UUID' })
  @ApiResponse({
    status: 200,
    description: 'Client system updated successfully',
    type: ClientSystems,
  })
  @Audit({
    action: AuditAction.UPDATE,
    module: AuditModule.SYSTEM_MANAGEMENT,
    resourceType: 'ClientSystem',
    description: 'Updated client system',
  })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateClientSystemDto: UpdateClientSystemDto,
    @Request() req: any,
  ): Promise<ClientSystems> {
    return this.clientSystemsService.update(id, updateClientSystemDto, req.user.userId);
  }

  @Patch(':id/access')
  @ApiOperation({ summary: 'Update last accessed timestamp for a client system' })
  @ApiParam({ name: 'id', description: 'Client system UUID' })
  @ApiResponse({
    status: 200,
    description: 'Last accessed timestamp updated successfully',
  })
  async updateLastAccessed(@Param('id', ParseUUIDPipe) id: string): Promise<{ message: string }> {
    await this.clientSystemsService.updateLastAccessed(id);
    return { message: 'Last accessed timestamp updated successfully' };
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a client system' })
  @ApiParam({ name: 'id', description: 'Client system UUID' })
  @ApiResponse({
    status: 200,
    description: 'Client system deleted successfully',
  })
  @Audit({
    action: AuditAction.DELETE,
    module: AuditModule.SYSTEM_MANAGEMENT,
    resourceType: 'ClientSystem',
    description: 'Deleted client system',
  })
  async remove(@Param('id', ParseUUIDPipe) id: string): Promise<{ message: string }> {
    await this.clientSystemsService.remove(id);
    return { message: 'Client system deleted successfully' };
  }
}
