import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ClientSystemsController } from './client-systems.controller';
import { ClientSystemsService } from './client-systems.service';
import { ClientSystems } from '../entities/client-systems.entity';

@Module({
  imports: [TypeOrmModule.forFeature([ClientSystems])],
  controllers: [ClientSystemsController],
  providers: [ClientSystemsService],
  exports: [ClientSystemsService],
})
export class ClientSystemsModule {}
