import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Param,
  Body,
  HttpCode,
  HttpStatus,
  NotFoundException,
  UseGuards,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiBody, ApiBearerAuth } from '@nestjs/swagger';
import { DepartmentService } from './department.service';
import { Department } from '../entities/department.entity';
import { CreateDepartmentDto } from 'src/dto/department/create-department.dto';
import { UpdateDepartmentDto } from 'src/dto/department/update-department.dto';
import { Audit } from 'src/common/interceptors/audit.interceptor';
import { AuditAction, AuditModule } from 'src/entities/audit-trail.entity';
import { JwtAuthGuard } from 'src/auth/guards/jwt-auth.guard';
import { AuthGuard } from '@nestjs/passport';


@ApiTags('Departments')
@ApiBearerAuth()
@UseGuards(AuthGuard('jwt'))
@Controller('department')
export class DepartmentController {
  constructor(private readonly departmentService: DepartmentService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new department' })
  @ApiResponse({ status: 201, description: 'Department created', type: Department })
  @Audit({
    action: AuditAction.CREATE,
    module: AuditModule.USER_MANAGEMENT,
    resourceType: 'Department',
    description: 'Created department',
  })
  @ApiBody({ type: CreateDepartmentDto })
  @ApiBearerAuth('JWT-auth')
  @UseGuards(JwtAuthGuard)
  async create(@Body() createDto: CreateDepartmentDto): Promise<Department> {
    return this.departmentService.create(createDto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all departments' })
  @ApiResponse({ status: 200, description: 'List of departments', type: [Department] })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.USER_MANAGEMENT,
    resourceType: 'Department',
    description: 'Viewed departments list',
  })
  async findAll(): Promise<Department[]> {
    return this.departmentService.findAll();
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a department by ID' })
  @ApiParam({ name: 'id', description: 'Department ID' })
  @ApiResponse({ status: 200, description: 'Department details', type: Department })
  @ApiResponse({ status: 404, description: 'Department not found' })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.USER_MANAGEMENT,
    resourceType: 'Department',
    description: 'Viewed department details',
  })
  async findOne(@Param('id') id: string): Promise<Department> {
    return this.departmentService.findOne(id);
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update a department by ID' })
  @ApiParam({ name: 'id', description: 'Department ID' })
  @ApiBody({ type: UpdateDepartmentDto })
  @ApiResponse({ status: 200, description: 'Department updated', type: Department })
  @ApiResponse({ status: 404, description: 'Department not found' })
  @Audit({
    action: AuditAction.UPDATE,
    module: AuditModule.USER_MANAGEMENT,
    resourceType: 'Department',
    description: 'Updated department',
  })
  @ApiBearerAuth('JWT-auth')
  @UseGuards(JwtAuthGuard)
  async update(@Param('id') id: string, @Body() updateDto: UpdateDepartmentDto): Promise<Department> {
    return this.departmentService.update(id, updateDto);
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Soft delete a department by ID' })
  @ApiParam({ name: 'id', description: 'Department ID' })
  @ApiResponse({ status: 204, description: 'Department deleted' })
  @ApiResponse({ status: 404, description: 'Department not found' })
  @Audit({
    action: AuditAction.DELETE,
    module: AuditModule.USER_MANAGEMENT,
    resourceType: 'Department',
    description: 'Deleted department',
  })
  @ApiBearerAuth('JWT-auth')
  @UseGuards(JwtAuthGuard)
  async remove(@Param('id') id: string): Promise<void> {
    return this.departmentService.remove(id);
  }
}
