import { <PERSON><PERSON><PERSON>E<PERSON>y, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>I<PERSON> } from "class-validator";
import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";

export class UpdateAddressDto {
  @ApiProperty({
    description: 'Address ID to update',
    example: 'a46c4216-ec16-47ab-b24c-bcceae6a2a00'
  })
  @IsNotEmpty({ message: "Address ID is required!"})
  @IsString({ message: "Address ID invalid!" })
  @IsUUID()
  address_id: string;

  @ApiPropertyOptional({
    description: 'Address type',
    example: 'business'
  })
  @IsOptional()
  @IsString({ message: "Address type invalid!" })
  address_type?: string;

  @ApiPropertyOptional({
    description: 'Entity type that owns this address',
    enum: ['applicant', 'application', 'stakeholder', 'contact_person', 'user'],
    example: 'applicant'
  })
  @IsOptional()
  @IsString({ message: "Entity type invalid!" })
  entity_type?: string;

  @ApiPropertyOptional({
    description: 'Entity ID that owns this address',
    example: 'a46c4216-ec16-47ab-b24c-bcceae6a2a00'
  })
  @IsOptional()
  @IsString({ message: "Entity ID invalid!" })
  entity_id?: string;

  @ApiPropertyOptional({
    description: 'Address line 1',
    example: '123 Main Street'
  })
  @IsOptional()
  @IsString({ message: "Address line 1 invalid!" })
  address_line_1?: string;

  @ApiPropertyOptional({
    description: 'Address line 2',
    example: 'Apartment 4B'
  })
  @IsOptional()
  @IsString({ message: "Address line 2 invalid!" })
  address_line_2?: string;

  @ApiPropertyOptional({
    description: 'Address line 3',
    example: 'Building Complex'
  })
  @IsOptional()
  @IsString({ message: "Address line 3 invalid!" })
  address_line_3?: string;

  @ApiPropertyOptional({
    description: 'Postal code',
    example: '101010',
    minLength: 6,
    maxLength: 9
  })
  @IsOptional()
  @IsString({ message: "Postal code invalid!" })
  @MinLength(6, { message: "Postal code must be at least 6 characters long!" })
  @MaxLength(9, { message: "Postal code must not exceed 9 characters!" })
  postal_code?: string;

  @ApiPropertyOptional({
    description: 'Country',
    example: 'Malawi',
    minLength: 3,
    maxLength: 50
  })
  @IsOptional()
  @IsString({ message: "Country invalid!" })
  @MinLength(3, { message: "Country must be at least 3 characters long!" })
  @MaxLength(50, { message: "Country must not exceed 50 characters!" })
  country?: string;

  @ApiPropertyOptional({
    description: 'City',
    example: 'Lilongwe',
    minLength: 3,
    maxLength: 50
  })
  @IsOptional()
  @IsString({ message: "City invalid!" })
  @MinLength(3, { message: "City must be at least 3 characters long!" })
  @MaxLength(50, { message: "City must not exceed 50 characters!" })
  city?: string;
}
